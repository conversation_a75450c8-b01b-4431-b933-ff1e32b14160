/**
 * Simple WhatsApp Service for MVP
 * 
 * Integración simplificada con Evolution API v2 para WhatsApp.
 * Diseñada para máxima simplicidad y funcionalidad MVP.
 * 
 * <AUTHOR> Development Team
 * @date 2025-01-28
 */

import { createClient } from '@/lib/supabase/server';
import type { SupabaseClient } from '@supabase/supabase-js';

// =====================================================
// TYPES
// =====================================================

export interface SimpleWhatsAppInstance {
  id: string;
  organization_id: string;
  name: string;
  display_name: string;
  evolution_instance_name: string;
  evolution_instance_id?: string;
  status: 'creating' | 'connecting' | 'connected' | 'disconnected' | 'error' | 'deleted';
  connection_state?: 'open' | 'connecting' | 'close';
  qr_code_base64?: string;
  qr_code_expires_at?: string;
  whatsapp_number?: string;
  whatsapp_name?: string;
  error_message?: string;
  created_at: string;
  updated_at: string;
  connected_at?: string;
}

export interface CreateInstanceRequest {
  displayName: string;
  organizationId: string;
}

export interface QRCodeResponse {
  success: boolean;
  qrCode?: string;
  status: 'available' | 'connecting' | 'connected' | 'error';
  expiresAt?: string;
  message?: string;
}

// =====================================================
// SIMPLE WHATSAPP SERVICE
// =====================================================

export class SimpleWhatsAppService {
  private supabase: SupabaseClient;
  private evolutionConfig: {
    baseUrl: string;
    apiKey: string;
  };

  constructor(supabase: SupabaseClient) {
    this.supabase = supabase;
    // Fix: Remove trailing slash from base URL to prevent double slashes
    const baseUrl = (process.env.EVOLUTION_API_BASE_URL || 'https://evo.torrecentral.com').replace(/\/$/, '');
    this.evolutionConfig = {
      baseUrl,
      apiKey: process.env.EVOLUTION_API_KEY || 'ixisatbi7f3p9m1ip37hibanq0vjq8nc'
    };

    console.log('🔧 Evolution API Config:', {
      baseUrl: this.evolutionConfig.baseUrl,
      apiKey: this.evolutionConfig.apiKey.substring(0, 10) + '...'
    });
  }

  /**
   * Crear nueva instancia WhatsApp
   */
  async createInstance(request: CreateInstanceRequest): Promise<SimpleWhatsAppInstance> {
    try {
      console.log('🚀 Creating simple WhatsApp instance:', request.displayName);

      // 1. Generar nombre único para Evolution API (máximo 100 caracteres)
      const timestamp = Date.now();
      const orgPrefix = request.displayName.toLowerCase()
        .replace(/[^a-z0-9]/g, '')
        .substring(0, 20);
      const evolutionName = `${orgPrefix}-wa-${timestamp}`.substring(0, 100);

      // 2. Crear instancia en Evolution API
      const evolutionResponse = await this.createEvolutionInstance(evolutionName);

      // 3. Configurar webhook para recibir actualizaciones de estado
      await this.configureWebhook(evolutionName, request.organizationId);

      // 4. Guardar en base de datos
      const { data: instance, error } = await this.supabase
        .from('whatsapp_instances_simple')
        .insert({
          organization_id: request.organizationId,
          name: evolutionName,
          display_name: request.displayName,
          evolution_instance_name: evolutionName,
          evolution_instance_id: evolutionResponse.instanceId,
          status: 'connecting'
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Database error:', error);
        throw new Error(`Database error: ${error.message}`);
      }

      console.log('✅ WhatsApp instance created successfully:', instance.id);
      return instance;

    } catch (error) {
      console.error('❌ Error creating WhatsApp instance:', error);
      throw new Error(`Failed to create WhatsApp instance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Obtener código QR para conexión
   */
  async getQRCode(instanceId: string): Promise<QRCodeResponse> {
    try {
      console.log('📱 Getting QR code for instance:', instanceId);

      // 1. Obtener instancia de la base de datos
      const { data: instance, error } = await this.supabase
        .from('whatsapp_instances_simple')
        .select('*')
        .eq('id', instanceId)
        .single();

      if (error || !instance) {
        throw new Error('Instance not found');
      }

      // 2. Verificar estado
      if (instance.status === 'connected') {
        return {
          success: true,
          status: 'connected',
          message: 'Instance already connected'
        };
      }

      // 3. Obtener QR code de Evolution API
      const qrData = await this.getEvolutionQRCode(instance.evolution_instance_name);

      // 4. Actualizar base de datos con QR code
      if (qrData.base64) {
        const expiresAt = new Date(Date.now() + 45000).toISOString(); // 45 segundos
        
        await this.supabase
          .from('whatsapp_instances_simple')
          .update({
            qr_code_base64: qrData.base64,
            qr_code_expires_at: expiresAt,
            status: 'connecting'
          })
          .eq('id', instanceId);

        return {
          success: true,
          qrCode: qrData.base64.startsWith('data:') ? qrData.base64 : `data:image/png;base64,${qrData.base64}`,
          status: 'available',
          expiresAt,
          message: 'QR code ready for scanning'
        };
      }

      return {
        success: true,
        status: 'connecting',
        message: 'QR code is being generated'
      };

    } catch (error) {
      console.error('❌ Error getting QR code:', error);
      
      // Actualizar estado de error
      await this.supabase
        .from('whatsapp_instances_simple')
        .update({
          status: 'error',
          error_message: error instanceof Error ? error.message : 'Unknown error'
        })
        .eq('id', instanceId);

      return {
        success: false,
        status: 'error',
        message: error instanceof Error ? error.message : 'Failed to get QR code'
      };
    }
  }

  /**
   * Obtener estado de instancia
   */
  async getInstanceStatus(instanceId: string): Promise<SimpleWhatsAppInstance | null> {
    try {
      const { data: instance, error } = await this.supabase
        .from('whatsapp_instances_simple')
        .select('*')
        .eq('id', instanceId)
        .single();

      if (error) {
        console.error('❌ Error getting instance:', error);
        return null;
      }

      return instance;
    } catch (error) {
      console.error('❌ Error getting instance status:', error);
      return null;
    }
  }

  /**
   * Listar instancias de una organización
   */
  async listInstances(organizationId: string): Promise<SimpleWhatsAppInstance[]> {
    try {
      const { data: instances, error } = await this.supabase
        .from('whatsapp_instances_simple')
        .select('*')
        .eq('organization_id', organizationId)
        .neq('status', 'deleted')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ Error listing instances:', error);
        return [];
      }

      return instances || [];
    } catch (error) {
      console.error('❌ Error listing instances:', error);
      return [];
    }
  }

  /**
   * Obtener una instancia específica
   */
  async getInstance(instanceId: string): Promise<SimpleWhatsAppInstance | null> {
    try {
      console.log('📋 Getting simple WhatsApp instance:', instanceId);

      const { data: instance, error } = await this.supabase
        .from('whatsapp_instances_simple')
        .select('*')
        .eq('id', instanceId)
        .neq('status', 'deleted')
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        console.error('❌ Database error:', error);
        throw new Error(`Database error: ${error.message}`);
      }

      console.log('✅ Found WhatsApp instance:', instance.id);
      return instance;

    } catch (error) {
      console.error('❌ Error getting WhatsApp instance:', error);
      throw new Error(`Failed to get WhatsApp instance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Eliminar una instancia
   */
  async deleteInstance(instanceId: string): Promise<void> {
    try {
      console.log('🗑️ Deleting simple WhatsApp instance:', instanceId);

      // 1. Obtener instancia para obtener el nombre de Evolution
      const instance = await this.getInstance(instanceId);
      if (!instance) {
        throw new Error('Instance not found');
      }

      // 2. Eliminar de Evolution API (opcional, puede fallar)
      try {
        await this.deleteEvolutionInstance(instance.evolution_instance_name);
      } catch (evolutionError) {
        console.warn('⚠️ Failed to delete from Evolution API (continuing):', evolutionError);
      }

      // 3. Marcar como eliminada en base de datos
      const { error } = await this.supabase
        .from('whatsapp_instances_simple')
        .update({
          status: 'deleted',
          updated_at: new Date().toISOString()
        })
        .eq('id', instanceId);

      if (error) {
        console.error('❌ Database error:', error);
        throw new Error(`Database error: ${error.message}`);
      }

      console.log('✅ WhatsApp instance deleted successfully:', instanceId);

    } catch (error) {
      console.error('❌ Error deleting WhatsApp instance:', error);
      throw new Error(`Failed to delete WhatsApp instance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // =====================================================
  // EVOLUTION API METHODS
  // =====================================================

  /**
   * Crear instancia en Evolution API
   */
  private async createEvolutionInstance(instanceName: string): Promise<{ instanceId: string }> {
    try {
      console.log('🔧 Creating Evolution API instance with payload:', {
        instanceName,
        qrcode: true,
        integration: 'WHATSAPP-BAILEYS'
      });

      const response = await fetch(`${this.evolutionConfig.baseUrl}/instance/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': this.evolutionConfig.apiKey
        },
        body: JSON.stringify({
          instanceName,
          qrcode: true,
          integration: 'WHATSAPP-BAILEYS'
        })
      });

      console.log('📡 Evolution API response status:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Evolution API error response:', errorText);
        throw new Error(`Evolution API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ Evolution instance created successfully:', result);

      return {
        instanceId: result.instance?.instanceId || result.instanceId || instanceName
      };
    } catch (error) {
      console.error('❌ Error creating Evolution instance:', error);
      throw error;
    }
  }

  /**
   * Obtener QR code de Evolution API
   */
  private async getEvolutionQRCode(instanceName: string): Promise<{ base64?: string; status?: string }> {
    try {
      const response = await fetch(`${this.evolutionConfig.baseUrl}/instance/connect/${instanceName}`, {
        method: 'GET',
        headers: {
          'apikey': this.evolutionConfig.apiKey
        }
      });

      if (!response.ok) {
        throw new Error(`Evolution API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('📱 Evolution QR response received');

      return {
        base64: result.base64,
        status: result.status || 'available'
      };
    } catch (error) {
      console.error('❌ Error getting Evolution QR code:', error);
      throw error;
    }
  }

  /**
   * Configurar webhook para recibir actualizaciones de estado
   */
  private async configureWebhook(instanceName: string, organizationId: string): Promise<void> {
    try {
      console.log('🔗 Configuring webhook for instance:', instanceName);

      const webhookUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001'}/api/whatsapp/simple/webhook/${organizationId}`;

      const response = await fetch(`${this.evolutionConfig.baseUrl}/webhook/instance`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': this.evolutionConfig.apiKey
        },
        body: JSON.stringify({
          instanceName,
          url: webhookUrl,
          webhook_by_events: false,
          webhook_base64: false,
          events: [
            'QRCODE_UPDATED',
            'CONNECTION_UPDATE',
            'STATUS_INSTANCE'
          ]
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.warn('⚠️ Failed to configure webhook (continuing without):', errorText);
        return;
      }

      console.log('✅ Webhook configured successfully:', webhookUrl);
    } catch (error) {
      console.warn('⚠️ Failed to configure webhook (continuing without):', error);
    }
  }

  /**
   * Eliminar instancia de Evolution API
   */
  private async deleteEvolutionInstance(instanceName: string): Promise<void> {
    try {
      console.log('🗑️ Deleting Evolution API instance:', instanceName);

      const response = await fetch(`${this.evolutionConfig.baseUrl}/instance/delete/${instanceName}`, {
        method: 'DELETE',
        headers: {
          'apikey': this.evolutionConfig.apiKey
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.warn('⚠️ Failed to delete Evolution instance:', errorText);
        throw new Error(`Evolution API error: ${response.status} ${response.statusText}`);
      }

      console.log('✅ Evolution instance deleted successfully:', instanceName);
    } catch (error) {
      console.error('❌ Error deleting Evolution instance:', error);
      throw error;
    }
  }

  /**
   * Actualizar estado de instancia desde webhook
   */
  async updateInstanceStatus(
    instanceName: string,
    connectionState: string,
    whatsappData?: {
      number?: string;
      name?: string;
      profilePicUrl?: string;
    }
  ): Promise<void> {
    try {
      console.log('🔄 Updating instance status:', { instanceName, connectionState, whatsappData });

      // Determinar el estado basado en el estado de conexión
      let status: SimpleWhatsAppInstance['status'];
      const updateData: any = {
        connection_state: connectionState,
        updated_at: new Date().toISOString()
      };

      switch (connectionState) {
        case 'open':
          status = 'connected';
          updateData.connected_at = new Date().toISOString();
          updateData.error_message = null;
          updateData.error_count = 0;

          // Actualizar datos de WhatsApp si están disponibles
          if (whatsappData?.number) {
            updateData.whatsapp_number = whatsappData.number;
          }
          if (whatsappData?.name) {
            updateData.whatsapp_name = whatsappData.name;
          }
          if (whatsappData?.profilePicUrl) {
            updateData.whatsapp_profile_pic_url = whatsappData.profilePicUrl;
          }
          break;

        case 'connecting':
          status = 'connecting';
          break;

        case 'close':
          status = 'disconnected';
          updateData.disconnected_at = new Date().toISOString();
          break;

        default:
          status = 'error';
          updateData.error_message = `Unknown connection state: ${connectionState}`;
      }

      updateData.status = status;

      // Actualizar en base de datos
      const { error } = await this.supabase
        .from('whatsapp_instances_simple')
        .update(updateData)
        .eq('evolution_instance_name', instanceName);

      if (error) {
        console.error('❌ Error updating instance status:', error);
        throw error;
      }

      console.log('✅ Instance status updated successfully:', { instanceName, status, connectionState });
    } catch (error) {
      console.error('❌ Error updating instance status:', error);
      throw error;
    }
  }
}

// =====================================================
// FACTORY FUNCTION
// =====================================================

/**
 * Crear instancia del servicio WhatsApp simplificado
 */
export async function createSimpleWhatsAppService(): Promise<SimpleWhatsAppService> {
  const supabase = await createClient();
  return new SimpleWhatsAppService(supabase);
}
